// Node.js script to generate all item detail pages
const fs = require('fs');
const path = require('path');

// Import the items database
const { itemsDatabase, getItemsByCategory } = require('./assets/js/items-database.js');

// HTML template for item detail pages
function generateItemPageHTML(item) {
    const relatedItems = getItemsByCategory(item.category)
        .filter(relatedItem => relatedItem.id !== item.id)
        .slice(0, 4);

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${item.name} - Natha Moorti Museum</title>
    <meta name="description" content="Explore the ${item.name}, a ${item.period} ${item.material.toLowerCase()} masterpiece from ${item.origin} at Natha Moorti Museum.">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/item-detail.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <!-- Breadcrumb -->
    <section class="breadcrumb-section">
        <div class="container">
            <nav class="breadcrumb">
                <a href="index.html">Home</a>
                <span>/</span>
                <a href="collections.html">Collections</a>
                <span>/</span>
                <a href="collections.html#${item.category.toLowerCase()}">${item.category}</a>
                <span>/</span>
                <span>${item.name}</span>
            </nav>
        </div>
    </section>

    <!-- Item Detail -->
    <section class="item-detail">
        <div class="container">
            <div class="item-detail-content">
                <div class="item-images">
                    <div class="main-image">
                        <img id="main-item-image" src="${item.images[0]}" alt="${item.name}">
                        <button class="zoom-btn" onclick="openImageZoom()">
                            <i class="fas fa-search-plus"></i>
                        </button>
                    </div>
                    
                    <div class="image-thumbnails">
                        ${item.images.map((image, index) => `
                        <div class="thumbnail ${index === 0 ? 'active' : ''}">
                            <img src="${image}" alt="View ${index + 1}">
                        </div>`).join('')}
                    </div>
                </div>
                
                <div class="item-info">
                    <div class="item-header">
                        <span class="item-category-badge">${item.category}</span>
                        <h1 class="item-title">${item.name}</h1>
                        <p class="item-subtitle">${item.subtitle}</p>
                    </div>
                    
                    <div class="item-description">
                        ${item.description.split('. ').map(sentence => sentence.trim() ? `<p>${sentence}${sentence.endsWith('.') ? '' : '.'}</p>` : '').join('')}
                    </div>
                    
                    <div class="item-details">
                        <h3>Details</h3>
                        <div class="details-grid">
                            <div class="detail-item">
                                <span class="detail-label">Period:</span>
                                <span class="detail-value">${item.period}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Material:</span>
                                <span class="detail-value">${item.material}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Dimensions:</span>
                                <span class="detail-value">${item.dimensions}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Origin:</span>
                                <span class="detail-value">${item.origin}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Acquisition:</span>
                                <span class="detail-value">${item.acquisition}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Catalog ID:</span>
                                <span class="detail-value">${item.catalog}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="item-actions">
                        <button class="btn btn-primary" onclick="shareItem()">
                            <i class="fas fa-share-alt"></i>
                            <span>Share</span>
                        </button>
                        <button class="btn btn-secondary" onclick="printItem()">
                            <i class="fas fa-print"></i>
                            <span>Print</span>
                        </button>
                        <button class="btn btn-secondary" onclick="addToFavorites()">
                            <i class="fas fa-heart"></i>
                            <span>Save</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Items -->
    <section class="related-items">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Related Items</h2>
                <p class="section-description">Discover more pieces from our ${item.category} collection</p>
            </div>
            
            <div class="related-grid">
                ${relatedItems.map(relatedItem => `
                <div class="related-item">
                    <div class="related-image">
                        <img src="${relatedItem.images[0]}" alt="${relatedItem.name}">
                    </div>
                    <div class="related-content">
                        <span class="related-category">${relatedItem.category}</span>
                        <h3>${relatedItem.name}</h3>
                        <a href="item-${relatedItem.id}.html" class="related-link">View Details</a>
                    </div>
                </div>`).join('')}
            </div>
        </div>
    </section>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <!-- JavaScript -->
    <script src="assets/js/common.js"></script>
    <script src="assets/js/items-database.js"></script>
    <script src="assets/js/item-detail.js"></script>
    <script>
        // Set current item ID for this page
        window.currentItemId = '${item.id}';
    </script>
</body>
</html>`;
}

// Generate all item pages
function generateAllItemPages() {
    console.log('Generating item detail pages...');
    
    Object.values(itemsDatabase).forEach(item => {
        const filename = `item-${item.id}.html`;
        const html = generateItemPageHTML(item);
        
        fs.writeFileSync(filename, html, 'utf8');
        console.log(`Generated: ${filename}`);
    });
    
    console.log('All item detail pages generated successfully!');
}

// Run the generator
if (require.main === module) {
    generateAllItemPages();
}

module.exports = { generateItemPageHTML, generateAllItemPages };
