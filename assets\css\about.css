/* About Page Specific Styles */

/* <PERSON> Header */
.page-header {
    position: relative;
    height: 60vh;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 120px;
    overflow: hidden;
}

.page-header-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.page-header-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(44, 24, 16, 0.8), rgba(139, 69, 19, 0.4));
}

.page-header-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white);
}

.breadcrumb {
    margin-bottom: 20px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.breadcrumb a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--white);
}

.breadcrumb span {
    margin: 0 10px;
    color: rgba(255, 255, 255, 0.6);
}

.page-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--white);
}

.page-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
}

/* About Introduction */
.about-intro {
    padding: 100px 0;
}

.about-intro-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-intro-text .lead {
    font-size: 1.3rem;
    color: var(--dark-color);
    margin-bottom: 30px;
}

.about-intro-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 25px;
    color: var(--gray-dark);
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 50px;
}

.stat-item {
    text-align: center;
    padding: 30px 20px;
    background-color: var(--gray-light);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: var(--font-primary);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--gray-dark);
    font-weight: 500;
}

.about-intro-image {
    position: relative;
}

.about-intro-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    box-shadow: var(--shadow);
}

/* Mission & Vision */
.mission-vision {
    padding: 100px 0;
    background-color: var(--gray-light);
}

.mission-vision-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.mission-card,
.vision-card,
.values-card {
    background-color: var(--white);
    padding: 40px 30px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.mission-card:hover,
.vision-card:hover,
.values-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.card-icon {
    width: 80px;
    height: 80px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    font-size: 30px;
    transition: var(--transition);
}

.mission-card:hover .card-icon,
.vision-card:hover .card-icon,
.values-card:hover .card-icon {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.mission-card h3,
.vision-card h3,
.values-card h3 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.mission-card p,
.vision-card p,
.values-card p {
    color: var(--gray-dark);
    line-height: 1.7;
}

/* History Timeline */
.history-timeline {
    padding: 100px 0;
}

.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: var(--primary-color);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 50px;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-year {
    width: 100px;
    height: 100px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    position: relative;
    z-index: 2;
    flex-shrink: 0;
}

.timeline-content {
    flex: 1;
    padding: 30px;
    background-color: var(--white);
    box-shadow: var(--shadow);
    margin: 0 30px;
    position: relative;
}

.timeline-item:nth-child(odd) .timeline-content::before {
    content: '';
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 15px solid var(--white);
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
}

.timeline-item:nth-child(even) .timeline-content::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-right: 15px solid var(--white);
    border-top: 15px solid transparent;
    border-bottom: 15px solid transparent;
}

.timeline-content h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.timeline-content p {
    color: var(--gray-dark);
    line-height: 1.6;
    margin-bottom: 0;
}

/* Team Section */
.team-section {
    padding: 100px 0;
    background-color: var(--gray-light);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.team-member {
    background-color: var(--white);
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    overflow: hidden;
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.member-image {
    position: relative;
    overflow: hidden;
    height: 300px;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.member-info {
    padding: 30px;
}

.member-info h3 {
    font-size: 1.4rem;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.member-role {
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 15px;
}

.member-info p {
    color: var(--gray-dark);
    line-height: 1.6;
    font-size: 14px;
}

/* Visit Information */
.visit-info {
    padding: 100px 0;
}

.visit-info-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.visit-info-text h2 {
    font-size: 2.5rem;
    margin-bottom: 25px;
    color: var(--dark-color);
}

.visit-info-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 40px;
    color: var(--gray-dark);
}

.visit-highlights {
    margin-bottom: 40px;
}

.highlight-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
}

.highlight-item i {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
    font-size: 20px;
}

.highlight-item h4 {
    margin-bottom: 8px;
    color: var(--dark-color);
    font-size: 1.2rem;
}

.highlight-item p {
    color: var(--gray-dark);
    margin-bottom: 0;
    font-size: 14px;
    line-height: 1.5;
}

.visit-info-image {
    position: relative;
}

.visit-info-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    box-shadow: var(--shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .about-intro-content,
    .visit-info-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .about-stats {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .mission-vision-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .timeline::before {
        left: 30px;
    }
    
    .timeline-item {
        flex-direction: row !important;
        padding-left: 80px;
    }
    
    .timeline-year {
        position: absolute;
        left: 0;
        width: 60px;
        height: 60px;
        font-size: 14px;
    }
    
    .timeline-content {
        margin: 0;
    }
    
    .timeline-content::before {
        left: -15px !important;
        border-right: 15px solid var(--white) !important;
        border-left: none !important;
    }
    
    .team-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .member-image {
        height: 250px;
    }
}

@media (max-width: 480px) {
    .page-header {
        height: 50vh;
        min-height: 300px;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .timeline-item {
        padding-left: 60px;
    }
    
    .timeline-year {
        width: 50px;
        height: 50px;
        font-size: 12px;
    }
}
