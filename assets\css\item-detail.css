/* Item Detail Page Styles */

/* Breadcrumb */
.breadcrumb-section {
    padding: var(--spacing-md) 0;
    margin-top: 120px;
    background-color: var(--gray-light);
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    color: var(--secondary-color);
    font-family: var(--font-accent);
}

.breadcrumb a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--primary-color);
}

.breadcrumb span {
    color: var(--gray-medium);
}

/* Item Detail */
.item-detail {
    padding: var(--spacing-xl) 0;
}

.item-detail-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: start;
}

/* Item Images */
.item-images {
    position: sticky;
    top: 140px;
}

.main-image {
    position: relative;
    margin-bottom: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    aspect-ratio: 1;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.zoom-btn {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    color: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
}

.main-image:hover .zoom-btn {
    opacity: 1;
}

.zoom-btn:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

.image-thumbnails {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: var(--spacing-sm);
}

.thumbnail {
    aspect-ratio: 1;
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
}

.thumbnail.active {
    border-color: var(--primary-color);
}

.thumbnail:hover {
    border-color: var(--secondary-color);
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Item Info */
.item-info {
    padding-left: var(--spacing-md);
}

.item-header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.item-category-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--accent-color);
    color: var(--white);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 600;
    font-family: var(--font-accent);
    margin-bottom: var(--spacing-sm);
    border-radius: 2px;
}

.item-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 400;
    margin-bottom: var(--spacing-xs);
    color: var(--primary-color);
    line-height: 1.2;
}

.item-subtitle {
    font-size: 1.125rem;
    color: var(--secondary-color);
    font-style: italic;
    font-family: var(--font-primary);
    margin-bottom: 0;
}

.item-description {
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
}

.item-description p {
    margin-bottom: var(--spacing-md);
    font-size: 1rem;
}

.item-details {
    margin-bottom: var(--spacing-lg);
}

.item-details h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.details-grid {
    display: grid;
    gap: var(--spacing-sm);
}

.detail-item {
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--primary-color);
    font-family: var(--font-accent);
    font-size: 0.875rem;
}

.detail-value {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

.item-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.item-actions .btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Related Items */
.related-items {
    padding: var(--spacing-xl) 0;
    background-color: var(--gray-light);
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.related-item {
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
}

.related-item:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.related-image {
    aspect-ratio: 1;
    overflow: hidden;
}

.related-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.related-item:hover .related-image img {
    transform: scale(1.05);
}

.related-content {
    padding: var(--spacing-md);
}

.related-category {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--accent-color);
    font-weight: 600;
    font-family: var(--font-accent);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.related-content h3 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.related-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-family: var(--font-accent);
    font-size: 0.875rem;
    transition: var(--transition);
}

.related-link:hover {
    color: var(--accent-color);
}

/* Image Zoom Modal */
.image-zoom-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.image-zoom-modal.active {
    opacity: 1;
    visibility: visible;
}

.zoom-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.zoom-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.zoom-close {
    position: absolute;
    top: -50px;
    right: 0;
    background: none;
    border: none;
    color: var(--white);
    font-size: 2rem;
    cursor: pointer;
    transition: var(--transition);
}

.zoom-close:hover {
    color: var(--accent-color);
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .item-detail-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .item-images {
        position: static;
    }
    
    .item-info {
        padding-left: 0;
    }
    
    .detail-item {
        grid-template-columns: 1fr;
        gap: var(--spacing-xs);
    }
    
    .item-actions {
        justify-content: center;
    }
    
    .related-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .breadcrumb-section {
        padding: var(--spacing-sm) 0;
    }
    
    .breadcrumb {
        font-size: 0.75rem;
        flex-wrap: wrap;
    }
    
    .item-title {
        font-size: 1.75rem;
    }
    
    .image-thumbnails {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .item-actions .btn {
        flex: 1;
        justify-content: center;
    }
}
