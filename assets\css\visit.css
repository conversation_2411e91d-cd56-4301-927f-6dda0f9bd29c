/* Visit Page Styles */

/* <PERSON> Header */
.page-header {
    padding: var(--spacing-xl) 0 var(--spacing-lg);
    margin-top: 120px;
    background: linear-gradient(135deg, var(--gray-light) 0%, var(--white) 100%);
    text-align: center;
}

.page-header-content {
    max-width: 600px;
    margin: 0 auto;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    color: var(--secondary-color);
    font-family: var(--font-accent);
    margin-bottom: var(--spacing-md);
}

.breadcrumb a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--primary-color);
}

.breadcrumb span {
    color: var(--gray-medium);
}

.page-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 300;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.page-description {
    font-size: 1.125rem;
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Visit Information */
.visit-info {
    padding: var(--spacing-xxl) 0;
}

.visit-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.visit-card {
    background-color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-align: center;
}

.visit-card:hover {
    box-shadow: var(--shadow-elegant);
    transform: translateY(-4px);
}

.visit-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: 2rem;
    transition: var(--transition);
}

.visit-card:hover .visit-icon {
    transform: scale(1.1);
}

.visit-card h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 400;
}

.visit-details {
    text-align: left;
}

.visit-details p {
    margin-bottom: var(--spacing-sm);
    color: var(--secondary-color);
}

.visit-details strong {
    color: var(--primary-color);
    font-weight: 600;
}

.visit-details ul {
    list-style: none;
    padding: 0;
    margin: var(--spacing-sm) 0 var(--spacing-md);
}

.visit-details li {
    padding: var(--spacing-xs) 0;
    color: var(--secondary-color);
    position: relative;
    padding-left: var(--spacing-md);
}

.visit-details li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* Visitor Guidelines */
.visitor-guidelines {
    padding: var(--spacing-xxl) 0;
    background-color: var(--gray-light);
}

.guidelines-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.guideline-item {
    background-color: var(--white);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    text-align: center;
}

.guideline-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: 1.5rem;
    color: var(--white);
}

.guideline-icon.allowed {
    background-color: #4CAF50;
}

.guideline-icon.restricted {
    background-color: #f44336;
}

.guideline-icon.info {
    background-color: #2196F3;
}

.guideline-item h4 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 500;
}

.guideline-item ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.guideline-item li {
    padding: var(--spacing-xs) 0;
    color: var(--secondary-color);
    position: relative;
    padding-left: var(--spacing-md);
}

.guideline-item li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* Facilities */
.facilities {
    padding: var(--spacing-xxl) 0;
}

.facilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.facility-item {
    text-align: center;
    padding: var(--spacing-lg);
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.facility-item:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-4px);
}

.facility-item i {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
    transition: var(--transition);
}

.facility-item:hover i {
    transform: scale(1.1);
    color: var(--primary-color);
}

.facility-item h4 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-weight: 500;
}

.facility-item p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Visit Contact */
.visit-contact {
    padding: var(--spacing-xxl) 0;
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: var(--white);
}

.visit-contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.contact-info h3,
.visit-cta h3 {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    color: var(--white);
    font-weight: 400;
}

.contact-info p,
.visit-cta p {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-lg);
    color: rgba(255, 255, 255, 0.9);
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.contact-method {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.contact-method i {
    width: 50px;
    height: 50px;
    background-color: var(--accent-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.contact-method strong {
    display: block;
    margin-bottom: var(--spacing-xs);
    color: var(--white);
    font-weight: 600;
}

.contact-method p {
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.8);
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.visit-cta .btn {
    background-color: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
}

.visit-cta .btn:hover {
    background-color: var(--accent-color);
    color: var(--white);
    border-color: var(--accent-color);
}

.visit-cta .btn-secondary {
    background-color: transparent;
    color: var(--white);
    border-color: var(--white);
}

.visit-cta .btn-secondary:hover {
    background-color: var(--white);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .visit-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .guidelines-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .facilities-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }
    
    .visit-contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }
    
    .contact-methods {
        align-items: center;
    }
    
    .contact-method {
        justify-content: center;
        text-align: left;
    }
    
    .cta-buttons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .visit-card,
    .guideline-item,
    .facility-item {
        padding: var(--spacing-md);
    }
    
    .visit-icon,
    .guideline-icon {
        width: 60px;
        height: 60px;
        font-size: 1.25rem;
    }
    
    .facility-item i {
        font-size: 2.5rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-buttons .btn {
        width: 100%;
        max-width: 250px;
    }
}
