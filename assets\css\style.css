/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #1a1a1a;
    --secondary-color: #6b6b6b;
    --accent-color: #d4af37;
    --dark-color: #0a0a0a;
    --light-color: #fafafa;
    --white: #ffffff;
    --black: #000000;
    --gray-light: #f5f5f5;
    --gray-medium: #e0e0e0;
    --gray-dark: #757575;
    --border-color: #e8e8e8;

    --font-primary: 'Cormorant Garamond', serif;
    --font-secondary: 'Inter', sans-serif;
    --font-accent: 'Space Grotesk', sans-serif;

    --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-fast: all 0.2s ease;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);
    --shadow-large: 0 20px 60px rgba(0, 0, 0, 0.1);

    --border-radius: 0;
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 2rem;
    --spacing-lg: 4rem;
    --spacing-xl: 6rem;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.7;
    color: var(--primary-color);
    background-color: var(--white);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.img-fluid {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 400;
    line-height: 1.3;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 300;
    letter-spacing: -0.02em;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 400;
    letter-spacing: -0.01em;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 400;
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
    font-weight: 500;
}

p {
    margin-bottom: var(--spacing-sm);
    color: var(--secondary-color);
    font-size: 1rem;
    line-height: 1.7;
}

.lead {
    font-size: clamp(1.125rem, 2vw, 1.25rem);
    font-weight: 400;
    color: var(--primary-color);
    line-height: 1.6;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-family: var(--font-accent);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    letter-spacing: 0.025em;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    min-height: 48px;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--dark-color);
    border-color: var(--dark-color);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--gray-light);
    border-color: var(--primary-color);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-outline-light {
    background-color: transparent;
    color: var(--white);
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-outline-light:hover {
    background-color: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
}

/* Section Styles */
.section-header {
    margin-bottom: 4rem;
}

.section-subtitle {
    display: block;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.1rem;
    color: var(--gray-dark);
    max-width: 600px;
    margin: 0 auto;
}

.text-center {
    text-align: center;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
}

.header-top {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-xs) 0;
    font-size: 0.75rem;
}

.header-top-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info span {
    margin-right: var(--spacing-md);
    font-family: var(--font-accent);
    font-weight: 400;
}

.contact-info i {
    margin-right: var(--spacing-xs);
    color: var(--accent-color);
}

.social-links a {
    color: var(--white);
    margin-left: var(--spacing-sm);
    transition: var(--transition);
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.social-links a:hover {
    color: var(--accent-color);
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar {
    padding: var(--spacing-sm) 0;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-right: var(--spacing-sm);
}

.logo-text h1 {
    font-size: 1.25rem;
    margin-bottom: 0;
    color: var(--primary-color);
    font-weight: 400;
    letter-spacing: -0.01em;
}

.logo-text span {
    font-size: 0.625rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-family: var(--font-accent);
    font-weight: 500;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-menu li {
    margin: 0 var(--spacing-md);
    position: relative;
}

.nav-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    transition: var(--transition);
    display: flex;
    align-items: center;
    font-family: var(--font-accent);
    padding: var(--spacing-xs) 0;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link i {
    margin-left: var(--spacing-xs);
    font-size: 0.625rem;
    transition: var(--transition);
}

.dropdown:hover .nav-link i {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    left: 0;
    background-color: var(--white);
    box-shadow: var(--shadow-large);
    list-style: none;
    padding: var(--spacing-sm) 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: block;
    padding: var(--spacing-xs) var(--spacing-sm);
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.875rem;
    font-family: var(--font-accent);
}

.dropdown-menu a:hover {
    background-color: var(--gray-light);
    color: var(--primary-color);
}

.nav-actions {
    display: flex;
    align-items: center;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    margin-left: 20px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--dark-color);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    padding: var(--spacing-xl) 0;
    margin-top: 120px;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
    width: 100%;
}

.hero-text {
    max-width: 600px;
}

.hero-title {
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: 300;
    line-height: 1.1;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    letter-spacing: -0.02em;
}

.hero-subtitle {
    font-size: clamp(1.125rem, 2vw, 1.5rem);
    font-weight: 400;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    font-family: var(--font-primary);
    font-style: italic;
}

.hero-description {
    font-size: 1.125rem;
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
    color: var(--secondary-color);
    max-width: 500px;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.hero-gallery {
    position: relative;
}

.hero-image-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: var(--spacing-sm);
    height: 600px;
}

.hero-image-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.hero-image-item.large {
    grid-row: 1 / -1;
}

.hero-image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.hero-image-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.hero-image-item:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--white);
    padding: var(--spacing-md);
    transform: translateY(100%);
    transition: var(--transition);
}

.hero-image-item:hover .image-overlay {
    transform: translateY(0);
}

.image-category {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--accent-color);
    font-weight: 600;
    font-family: var(--font-accent);
}

.image-overlay h3 {
    font-size: 1.25rem;
    margin-bottom: 0;
    color: var(--white);
    font-weight: 400;
}

/* Museum Stats */
.museum-stats {
    padding: var(--spacing-xl) 0;
    background-color: var(--gray-light);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
}

.stat-number {
    font-size: clamp(2.5rem, 4vw, 3.5rem);
    font-weight: 300;
    color: var(--primary-color);
    font-family: var(--font-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--secondary-color);
    font-weight: 500;
    font-family: var(--font-accent);
}

/* About Section */
.about {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-features {
    margin-top: 40px;
}

.feature {
    display: flex;
    margin-bottom: 30px;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 24px;
}

.feature-content h3 {
    margin-bottom: 10px;
    color: var(--dark-color);
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    box-shadow: var(--shadow);
}

/* Collections Section */
.collections {
    padding: var(--spacing-xl) 0;
}

.collections-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.collection-card {
    border: 1px solid var(--border-color);
    transition: var(--transition);
    background-color: var(--white);
}

.collection-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.collection-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    align-items: center;
}

.collection-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
    border-radius: var(--border-radius);
}

.collection-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.collection-card:hover .collection-image img {
    transform: scale(1.05);
}

.collection-info {
    position: relative;
}

.collection-number {
    font-size: 4rem;
    font-weight: 300;
    color: var(--gray-medium);
    font-family: var(--font-primary);
    position: absolute;
    top: -2rem;
    left: -1rem;
    z-index: 1;
    line-height: 1;
}

.collection-info h3 {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
    position: relative;
    z-index: 2;
}

.collection-info p {
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

.collection-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-md);
}

.item-count {
    font-size: 0.875rem;
    color: var(--secondary-color);
    font-weight: 500;
    font-family: var(--font-accent);
}

.collection-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-family: var(--font-accent);
    font-size: 0.875rem;
    transition: var(--transition);
}

.collection-link:hover {
    color: var(--accent-color);
}

/* Featured Items */
.featured-items {
    padding: var(--spacing-xl) 0;
    background-color: var(--gray-light);
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.featured-item {
    background-color: var(--white);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    overflow: hidden;
}

.featured-item:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-4px);
}

.featured-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.featured-item:hover .featured-image img {
    transform: scale(1.05);
}

.featured-content {
    padding: var(--spacing-md);
}

.featured-category {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--accent-color);
    font-weight: 600;
    font-family: var(--font-accent);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.featured-content h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.featured-content p {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
    font-size: 0.9rem;
}

.featured-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-family: var(--font-accent);
    font-size: 0.875rem;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.featured-link:hover {
    color: var(--accent-color);
}

.featured-link::after {
    content: '→';
    transition: var(--transition);
}

.featured-link:hover::after {
    transform: translateX(4px);
}

/* Gallery Preview */
.gallery-preview {
    padding: var(--spacing-xl) 0;
    background-color: var(--white);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    height: 300px;
    cursor: pointer;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

/* Contact Section */
.contact {
    padding: 100px 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-info h2 {
    margin-bottom: 40px;
    color: var(--dark-color);
}

.contact-item {
    display: flex;
    margin-bottom: 30px;
}

.contact-item i {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.contact-item h3 {
    margin-bottom: 10px;
    color: var(--dark-color);
}

.contact-form {
    background-color: var(--gray-light);
    padding: 40px;
}

.contact-form h3 {
    margin-bottom: 30px;
    color: var(--dark-color);
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid var(--gray-medium);
    background-color: var(--white);
    font-family: var(--font-secondary);
    font-size: 14px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.footer-logo img {
    width: 40px;
    height: 40px;
    margin-right: 15px;
}

.footer-logo h3 {
    color: var(--white);
    margin-bottom: 0;
}

.footer-section h4 {
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--secondary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    text-align: center;
    color: var(--gray-medium);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .hero-image-grid {
        height: 400px;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
    }

    .hero-image-item.large {
        grid-row: 1 / 2;
        grid-column: 1 / -1;
    }

    .collection-content {
        grid-template-columns: 250px 1fr;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .hero {
        padding: var(--spacing-lg) 0;
        margin-top: 100px;
    }

    .hero-content {
        gap: var(--spacing-md);
    }

    .hero-image-grid {
        height: 300px;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
    }

    .hero-image-item.large {
        grid-row: 1 / 2;
        grid-column: 1 / -1;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-sm);
    }

    .collection-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
        text-align: center;
    }

    .collection-image {
        max-width: 200px;
        margin: 0 auto;
    }

    .featured-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }

    .hero-actions .btn {
        min-width: 200px;
    }
}

@media (max-width: 480px) {
    .hero-image-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(4, 1fr);
        height: 400px;
    }

    .hero-image-item.large {
        grid-row: 1 / 3;
        grid-column: 1;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .collection-number {
        font-size: 2.5rem;
        top: -1rem;
        left: 0;
    }

    .featured-grid {
        grid-template-columns: 1fr;
    }
}
