/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #8B4513;
    --secondary-color: #D2691E;
    --accent-color: #CD853F;
    --dark-color: #2C1810;
    --light-color: #F5F5DC;
    --white: #FFFFFF;
    --black: #000000;
    --gray-light: #F8F8F8;
    --gray-medium: #CCCCCC;
    --gray-dark: #666666;
    
    --font-primary: 'Playfair Display', serif;
    --font-secondary: 'Inter', sans-serif;
    
    --transition: all 0.3s ease;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.img-fluid {
    max-width: 100%;
    height: auto;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.8rem; }
h4 { font-size: 1.4rem; }

p {
    margin-bottom: 1rem;
    color: var(--gray-dark);
}

.lead {
    font-size: 1.2rem;
    font-weight: 400;
    color: var(--dark-color);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border: none;
    border-radius: 0;
    font-family: var(--font-secondary);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--dark-color);
    transform: translateY(-2px);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-outline-light {
    background-color: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-outline-light:hover {
    background-color: var(--white);
    color: var(--primary-color);
}

/* Section Styles */
.section-header {
    margin-bottom: 4rem;
}

.section-subtitle {
    display: block;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.1rem;
    color: var(--gray-dark);
    max-width: 600px;
    margin: 0 auto;
}

.text-center {
    text-align: center;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--white);
    box-shadow: var(--shadow);
}

.header-top {
    background-color: var(--dark-color);
    color: var(--white);
    padding: 10px 0;
    font-size: 14px;
}

.header-top-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info span {
    margin-right: 30px;
}

.contact-info i {
    margin-right: 8px;
    color: var(--secondary-color);
}

.social-links a {
    color: var(--white);
    margin-left: 15px;
    transition: var(--transition);
}

.social-links a:hover {
    color: var(--secondary-color);
}

.navbar {
    padding: 15px 0;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo-img {
    width: 50px;
    height: 50px;
    object-fit: contain;
    margin-right: 15px;
}

.logo-text h1 {
    font-size: 1.5rem;
    margin-bottom: 0;
    color: var(--primary-color);
}

.logo-text span {
    font-size: 12px;
    color: var(--gray-dark);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin: 0 20px;
    position: relative;
}

.nav-link {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 1px;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link i {
    margin-left: 5px;
    font-size: 10px;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    box-shadow: var(--shadow);
    list-style: none;
    padding: 20px 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: var(--transition);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: block;
    padding: 10px 20px;
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background-color: var(--gray-light);
    color: var(--primary-color);
}

.nav-actions {
    display: flex;
    align-items: center;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    margin-left: 20px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--dark-color);
    margin: 3px 0;
    transition: var(--transition);
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    overflow: hidden;
    margin-top: 120px;
}

.hero-slider {
    position: relative;
    height: 100%;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.hero-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(44, 24, 16, 0.7), rgba(139, 69, 19, 0.3));
}

.hero-content {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    z-index: 2;
}

.hero-text {
    max-width: 600px;
    color: var(--white);
}

.hero-subtitle {
    display: block;
    font-size: 16px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 3px;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--white);
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.hero-actions {
    display: flex;
    gap: 20px;
}

.hero-nav {
    position: absolute;
    bottom: 30px;
    right: 30px;
    z-index: 3;
    display: flex;
    gap: 10px;
}

.hero-nav-btn {
    width: 50px;
    height: 50px;
    background-color: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: var(--white);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-nav-btn:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* About Section */
.about {
    padding: 100px 0;
    background-color: var(--gray-light);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-features {
    margin-top: 40px;
}

.feature {
    display: flex;
    margin-bottom: 30px;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 24px;
}

.feature-content h3 {
    margin-bottom: 10px;
    color: var(--dark-color);
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    box-shadow: var(--shadow);
}

/* Collections Section */
.collections {
    padding: 100px 0;
}

.collections-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.collection-card {
    position: relative;
    overflow: hidden;
    height: 400px;
    cursor: pointer;
}

.collection-image {
    position: relative;
    width: 100%;
    height: 100%;
}

.collection-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.collection-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(44, 24, 16, 0.8), rgba(139, 69, 19, 0.6));
    color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    opacity: 0;
    transition: var(--transition);
}

.collection-card:hover .collection-overlay {
    opacity: 1;
}

.collection-card:hover .collection-image img {
    transform: scale(1.1);
}

.collection-overlay h3 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.collection-overlay p {
    font-size: 1.1rem;
    margin-bottom: 25px;
    color: rgba(255, 255, 255, 0.9);
}

/* Gallery Preview */
.gallery-preview {
    padding: 100px 0;
    background-color: var(--gray-light);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    height: 300px;
    cursor: pointer;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

/* Contact Section */
.contact {
    padding: 100px 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-info h2 {
    margin-bottom: 40px;
    color: var(--dark-color);
}

.contact-item {
    display: flex;
    margin-bottom: 30px;
}

.contact-item i {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.contact-item h3 {
    margin-bottom: 10px;
    color: var(--dark-color);
}

.contact-form {
    background-color: var(--gray-light);
    padding: 40px;
}

.contact-form h3 {
    margin-bottom: 30px;
    color: var(--dark-color);
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid var(--gray-medium);
    background-color: var(--white);
    font-family: var(--font-secondary);
    font-size: 14px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.footer-logo img {
    width: 40px;
    height: 40px;
    margin-right: 15px;
}

.footer-logo h3 {
    color: var(--white);
    margin-bottom: 0;
}

.footer-section h4 {
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--secondary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    text-align: center;
    color: var(--gray-medium);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .collections-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-actions {
        flex-direction: column;
        gap: 15px;
    }
    
    .hero-actions .btn {
        text-align: center;
    }
}
