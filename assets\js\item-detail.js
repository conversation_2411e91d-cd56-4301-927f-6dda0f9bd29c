// Item Detail Page JavaScript

// Sample item data
const itemData = {
    'div001': {
        category: 'Divinity',
        name: 'Sacred Deity Sculpture',
        subtitle: 'Ancient Divine Representation',
        description: 'This magnificent sculpture represents the pinnacle of divine artistry, showcasing the perfect harmony between spiritual devotion and artistic excellence. Carved from pristine stone, this piece embodies centuries of religious tradition and masterful craftsmanship.',
        period: '12th-13th Century',
        material: 'Carved Stone',
        dimensions: '45cm x 30cm x 20cm',
        origin: 'Northern India',
        acquisition: '1985',
        catalog: 'NMM-DIV-001',
        images: [
            'Data/Product Photos/Divinity/25a559be-aa7b-4d3a-981c-e3576b0c21ef.jpeg',
            'Data/Product Photos/Divinity/2b7572ff-d063-45d9-af74-b941bd083d81.jpeg',
            'Data/Product Photos/Divinity/1486d96b-d890-493e-bfbe-36f3ebb739e2.jpeg'
        ]
    },
    'div002': {
        category: 'Divinity',
        name: 'Spiritual Guardian',
        subtitle: 'Protective Deity Figure',
        description: 'This protective deity figure showcases the intricate craftsmanship and spiritual significance of ancient sculptural traditions. The guardian represents divine protection and spiritual guidance.',
        period: '11th-12th Century',
        material: 'Bronze',
        dimensions: '38cm x 25cm x 18cm',
        origin: 'Southern India',
        acquisition: '1987',
        catalog: 'NMM-DIV-002',
        images: [
            'Data/Product Photos/Divinity/1a6213a9-eee5-4f66-8918-b92456b280d2.jpeg',
            'Data/Product Photos/Divinity/1f917925-364b-4087-a626-d3bc4fdadd3e.jpeg',
            'Data/Product Photos/Divinity/23b8647a-01e4-4cbe-827f-5dc9f9c1a5df.jpeg'
        ]
    },
    'art001': {
        category: 'Artifacts',
        name: 'Ceremonial Vessel',
        subtitle: 'Ancient Ritual Container',
        description: 'An exquisite example of ancient metalwork, this ceremonial piece reflects the sophisticated artistic traditions of its era. Used in religious ceremonies and rituals.',
        period: '10th-11th Century',
        material: 'Copper Alloy',
        dimensions: '25cm x 25cm x 30cm',
        origin: 'Western India',
        acquisition: '1990',
        catalog: 'NMM-ART-001',
        images: [
            'Data/Product Photos/Artefacts/302026b0-f188-4cf9-a5e0-a86363a91327.jpeg',
            'Data/Product Photos/Artefacts/77ba796f-27e2-487f-a9b4-07c149921578.jpeg',
            'Data/Product Photos/Artefacts/90533560-6d2a-44be-87a2-61ff3fad1ab0.jpeg'
        ]
    }
};

document.addEventListener('DOMContentLoaded', function() {
    initItemDetail();
    initImageGallery();
    initItemActions();
});

// Initialize item detail page
function initItemDetail() {
    const urlParams = new URLSearchParams(window.location.search);
    const itemId = urlParams.get('id') || 'div001';
    
    const item = itemData[itemId];
    if (item) {
        populateItemData(item);
    } else {
        // Default to first item if ID not found
        populateItemData(itemData['div001']);
    }
}

// Populate item data
function populateItemData(item) {
    // Update page title
    document.title = `${item.name} - Natha Moorti Museum`;
    
    // Update breadcrumb
    document.getElementById('item-category').textContent = item.category;
    document.getElementById('item-name').textContent = item.name;
    
    // Update main content
    document.getElementById('category-badge').textContent = item.category;
    document.getElementById('item-title').textContent = item.name;
    document.getElementById('item-subtitle').textContent = item.subtitle;
    document.getElementById('item-description-text').textContent = item.description;
    
    // Update details
    document.getElementById('item-period').textContent = item.period;
    document.getElementById('item-material').textContent = item.material;
    document.getElementById('item-dimensions').textContent = item.dimensions;
    document.getElementById('item-origin').textContent = item.origin;
    document.getElementById('item-acquisition').textContent = item.acquisition;
    document.getElementById('item-catalog').textContent = item.catalog;
    
    // Update images
    if (item.images && item.images.length > 0) {
        updateItemImages(item.images, item.name);
    }
}

// Update item images
function updateItemImages(images, itemName) {
    const mainImage = document.getElementById('main-item-image');
    const thumbnailContainer = document.querySelector('.image-thumbnails');
    
    // Update main image
    mainImage.src = images[0];
    mainImage.alt = itemName;
    
    // Clear and update thumbnails
    thumbnailContainer.innerHTML = '';
    
    images.forEach((imageSrc, index) => {
        const thumbnail = document.createElement('div');
        thumbnail.className = `thumbnail ${index === 0 ? 'active' : ''}`;
        thumbnail.innerHTML = `<img src="${imageSrc}" alt="View ${index + 1}">`;
        
        thumbnail.addEventListener('click', function() {
            // Update main image
            mainImage.src = imageSrc;
            
            // Update active thumbnail
            document.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
            this.classList.add('active');
        });
        
        thumbnailContainer.appendChild(thumbnail);
    });
}

// Initialize image gallery functionality
function initImageGallery() {
    const thumbnails = document.querySelectorAll('.thumbnail');
    const mainImage = document.getElementById('main-item-image');
    
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            const img = this.querySelector('img');
            
            // Update main image
            mainImage.src = img.src;
            mainImage.alt = img.alt;
            
            // Update active state
            thumbnails.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// Initialize item actions
function initItemActions() {
    // Add event listeners for action buttons
    const shareBtn = document.querySelector('[onclick="shareItem()"]');
    const printBtn = document.querySelector('[onclick="printItem()"]');
    const favoriteBtn = document.querySelector('[onclick="addToFavorites()"]');
    
    if (shareBtn) {
        shareBtn.removeAttribute('onclick');
        shareBtn.addEventListener('click', shareItem);
    }
    
    if (printBtn) {
        printBtn.removeAttribute('onclick');
        printBtn.addEventListener('click', printItem);
    }
    
    if (favoriteBtn) {
        favoriteBtn.removeAttribute('onclick');
        favoriteBtn.addEventListener('click', addToFavorites);
    }
}

// Share item functionality
function shareItem() {
    if (navigator.share) {
        const itemTitle = document.getElementById('item-title').textContent;
        const itemCategory = document.getElementById('category-badge').textContent;
        
        navigator.share({
            title: `${itemTitle} - Natha Moorti Museum`,
            text: `Check out this ${itemCategory} piece from Natha Moorti Museum`,
            url: window.location.href
        }).catch(console.error);
    } else {
        // Fallback: copy URL to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            showNotification('Link copied to clipboard!', 'success');
        }).catch(() => {
            showNotification('Unable to copy link', 'error');
        });
    }
}

// Print item functionality
function printItem() {
    window.print();
}

// Add to favorites functionality
function addToFavorites() {
    const itemId = new URLSearchParams(window.location.search).get('id') || 'div001';
    const itemTitle = document.getElementById('item-title').textContent;
    
    // Get existing favorites from localStorage
    let favorites = JSON.parse(localStorage.getItem('museum-favorites') || '[]');
    
    // Check if already in favorites
    const existingIndex = favorites.findIndex(fav => fav.id === itemId);
    
    if (existingIndex === -1) {
        // Add to favorites
        favorites.push({
            id: itemId,
            title: itemTitle,
            category: document.getElementById('category-badge').textContent,
            image: document.getElementById('main-item-image').src,
            addedDate: new Date().toISOString()
        });
        
        localStorage.setItem('museum-favorites', JSON.stringify(favorites));
        showNotification('Added to favorites!', 'success');
        
        // Update button state
        const favoriteBtn = document.querySelector('[onclick="addToFavorites()"]') || 
                           document.querySelector('.item-actions .btn:last-child');
        if (favoriteBtn) {
            favoriteBtn.innerHTML = '<i class="fas fa-heart" style="color: #d4af37;"></i> Saved';
            favoriteBtn.disabled = true;
        }
    } else {
        showNotification('Already in favorites!', 'info');
    }
}

// Open image zoom modal
function openImageZoom() {
    const mainImage = document.getElementById('main-item-image');
    const modal = createZoomModal(mainImage.src, mainImage.alt);
    document.body.appendChild(modal);
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
    
    // Show modal
    setTimeout(() => {
        modal.classList.add('active');
    }, 100);
}

// Create zoom modal
function createZoomModal(imageSrc, imageAlt) {
    const modal = document.createElement('div');
    modal.className = 'image-zoom-modal';
    modal.innerHTML = `
        <div class="zoom-content">
            <img src="${imageSrc}" alt="${imageAlt}" class="zoom-image">
            <button class="zoom-close">&times;</button>
        </div>
    `;
    
    // Close functionality
    const closeBtn = modal.querySelector('.zoom-close');
    closeBtn.addEventListener('click', closeZoomModal);
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeZoomModal();
        }
    });
    
    // Close on escape key
    const escapeHandler = function(e) {
        if (e.key === 'Escape') {
            closeZoomModal();
        }
    };
    document.addEventListener('keydown', escapeHandler);
    
    function closeZoomModal() {
        modal.classList.remove('active');
        document.body.style.overflow = '';
        document.removeEventListener('keydown', escapeHandler);
        
        setTimeout(() => {
            if (document.body.contains(modal)) {
                document.body.removeChild(modal);
            }
        }, 300);
    }
    
    return modal;
}

// Show notification
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <div class="notification-icon">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            </div>
            <div class="notification-message">${message}</div>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 0;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 400px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        overflow: hidden;
    `;
    
    // Style notification content
    const content = notification.querySelector('.notification-content');
    content.style.cssText = `
        display: flex;
        align-items: center;
        padding: 15px 20px;
        gap: 12px;
    `;
    
    // Style close button
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.style.cssText = `
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s ease;
        flex-shrink: 0;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Close functionality
    closeBtn.addEventListener('click', function() {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    });
    
    // Auto close after 3 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 3000);
}

// Check if item is in favorites on page load
document.addEventListener('DOMContentLoaded', function() {
    const itemId = new URLSearchParams(window.location.search).get('id') || 'div001';
    const favorites = JSON.parse(localStorage.getItem('museum-favorites') || '[]');
    
    if (favorites.some(fav => fav.id === itemId)) {
        const favoriteBtn = document.querySelector('.item-actions .btn:last-child');
        if (favoriteBtn) {
            favoriteBtn.innerHTML = '<i class="fas fa-heart" style="color: #d4af37;"></i> Saved';
            favoriteBtn.disabled = true;
        }
    }
});
