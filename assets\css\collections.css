/* Collections Page Specific Styles */

/* <PERSON> Header */
.page-header {
    position: relative;
    height: 60vh;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 120px;
    overflow: hidden;
}

.page-header-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.page-header-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(44, 24, 16, 0.8), rgba(139, 69, 19, 0.4));
}

.page-header-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white);
}

.breadcrumb {
    margin-bottom: 20px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.breadcrumb a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--white);
}

.breadcrumb span {
    margin: 0 10px;
    color: rgba(255, 255, 255, 0.6);
}

.page-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--white);
}

.page-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
}

/* Collections Filter */
.collections-filter {
    padding: 60px 0;
    background-color: var(--gray-light);
}

.filter-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 12px 30px;
    background-color: var(--white);
    border: 2px solid var(--gray-medium);
    color: var(--dark-color);
    font-family: var(--font-secondary);
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: var(--transition);
    border-radius: 0;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Collection Sections */
.collection-section {
    padding: 80px 0;
}

.collection-section:nth-child(even) {
    background-color: var(--gray-light);
}

.collection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.collection-item {
    background-color: var(--white);
    border-radius: 0;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    cursor: pointer;
}

.collection-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.item-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(44, 24, 16, 0.7), rgba(139, 69, 19, 0.5));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.collection-item:hover .item-overlay {
    opacity: 1;
}

.collection-item:hover .item-image img {
    transform: scale(1.1);
}

.view-btn {
    width: 60px;
    height: 60px;
    background-color: var(--white);
    border: none;
    border-radius: 50%;
    color: var(--primary-color);
    font-size: 20px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

.item-info {
    padding: 25px;
}

.item-info h3 {
    font-size: 1.4rem;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.item-info p {
    color: var(--gray-dark);
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Filter Animation */
.collection-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.collection-item.visible {
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease;
}

/* Lightbox Styles */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background: var(--white);
    padding: 20px;
    border-radius: 0;
}

.lightbox-image {
    width: 100%;
    max-width: 800px;
    height: auto;
    display: block;
}

.lightbox-info {
    padding: 20px 0 0;
    text-align: center;
}

.lightbox-info h3 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.lightbox-info p {
    color: var(--gray-dark);
    font-size: 16px;
    line-height: 1.6;
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    color: var(--white);
    font-size: 30px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-close:hover {
    color: var(--secondary-color);
    transform: scale(1.1);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-medium);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .page-description {
        font-size: 1rem;
    }
    
    .filter-tabs {
        gap: 10px;
    }
    
    .filter-btn {
        padding: 10px 20px;
        font-size: 12px;
    }
    
    .collection-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .item-image {
        height: 200px;
    }
    
    .item-info {
        padding: 20px;
    }
    
    .item-info h3 {
        font-size: 1.2rem;
    }
    
    .lightbox-content {
        max-width: 95%;
        max-height: 95%;
        padding: 15px;
    }
    
    .lightbox-close {
        top: -40px;
        right: -10px;
    }
}

@media (max-width: 480px) {
    .page-header {
        height: 50vh;
        min-height: 300px;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .collection-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-tabs {
        flex-direction: column;
        align-items: center;
    }
    
    .filter-btn {
        width: 200px;
        text-align: center;
    }
}

/* Scroll Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.collection-item {
    animation: fadeInUp 0.6s ease forwards;
}

.collection-item:nth-child(1) { animation-delay: 0.1s; }
.collection-item:nth-child(2) { animation-delay: 0.2s; }
.collection-item:nth-child(3) { animation-delay: 0.3s; }
.collection-item:nth-child(4) { animation-delay: 0.4s; }
.collection-item:nth-child(5) { animation-delay: 0.5s; }
.collection-item:nth-child(6) { animation-delay: 0.6s; }

/* Hover Effects */
.collection-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
    z-index: 1;
}

.collection-item:hover::before {
    left: 100%;
}

/* Category Badges */
.category-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: var(--primary-color);
    color: var(--white);
    padding: 5px 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    z-index: 2;
}

/* Search and Sort */
.collection-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 20px;
}

.search-box {
    position: relative;
    max-width: 300px;
    flex: 1;
}

.search-box input {
    width: 100%;
    padding: 12px 40px 12px 15px;
    border: 2px solid var(--gray-medium);
    background-color: var(--white);
    font-family: var(--font-secondary);
    font-size: 14px;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-dark);
}

.sort-dropdown {
    position: relative;
}

.sort-dropdown select {
    padding: 12px 15px;
    border: 2px solid var(--gray-medium);
    background-color: var(--white);
    font-family: var(--font-secondary);
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.sort-dropdown select:focus {
    outline: none;
    border-color: var(--primary-color);
}
