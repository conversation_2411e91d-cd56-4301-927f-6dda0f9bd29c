// Gallery Page JavaScript

let currentLightboxIndex = 0;
let galleryItems = [];

document.addEventListener('DOMContentLoaded', function() {
    initGalleryFilters();
    initViewControls();
    initLazyLoading();
    initLoadMore();
    updateGalleryItems();
});

// Initialize gallery filters
function initGalleryFilters() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter items
            galleryItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.classList.remove('hidden');
                    item.classList.add('visible');
                    item.style.display = 'block';
                } else {
                    item.classList.add('hidden');
                    item.classList.remove('visible');
                    setTimeout(() => {
                        if (item.classList.contains('hidden')) {
                            item.style.display = 'none';
                        }
                    }, 300);
                }
            });
            
            // Update gallery items array for lightbox
            updateGalleryItems();
        });
    });
}

// Initialize view controls
function initViewControls() {
    const viewBtns = document.querySelectorAll('.view-btn');
    const galleryGrid = document.getElementById('galleryGrid');
    
    viewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const view = this.getAttribute('data-view');
            
            // Update active button
            viewBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Update grid layout
            if (view === 'masonry') {
                galleryGrid.classList.add('masonry');
            } else {
                galleryGrid.classList.remove('masonry');
            }
        });
    });
}

// Initialize lazy loading
function initLazyLoading() {
    const images = document.querySelectorAll('.gallery-item img[loading="lazy"]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.src; // Trigger loading
                img.classList.add('loaded');
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => {
        imageObserver.observe(img);
    });
}

// Initialize load more functionality
function initLoadMore() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            // Simulate loading more items
            this.textContent = 'Loading...';
            this.disabled = true;
            
            setTimeout(() => {
                // In a real implementation, this would load more items from the server
                this.textContent = 'No more items';
                this.disabled = true;
            }, 1000);
        });
    }
}

// Update gallery items array for lightbox navigation
function updateGalleryItems() {
    galleryItems = Array.from(document.querySelectorAll('.gallery-item:not([style*="display: none"])'));
}

// Open lightbox
function openLightbox(button) {
    const galleryItem = button.closest('.gallery-item');
    const img = galleryItem.querySelector('img');
    const category = galleryItem.querySelector('.gallery-category').textContent;
    const title = galleryItem.querySelector('h3').textContent;
    
    // Find current index
    currentLightboxIndex = galleryItems.indexOf(galleryItem);
    
    // Create lightbox
    const lightbox = createLightbox(img.src, img.alt, category, title);
    document.body.appendChild(lightbox);
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
    
    // Show lightbox
    setTimeout(() => {
        lightbox.classList.add('active');
    }, 100);
}

// Create lightbox element
function createLightbox(imageSrc, imageAlt, category, title) {
    const lightbox = document.createElement('div');
    lightbox.className = 'lightbox';
    lightbox.innerHTML = `
        <div class="lightbox-content">
            <img src="${imageSrc}" alt="${imageAlt}" class="lightbox-image">
            <div class="lightbox-info">
                <span class="lightbox-category">${category}</span>
                <h3>${title}</h3>
                <div class="lightbox-actions">
                    <a href="item-detail.html?id=${generateItemId(category, title)}" class="btn btn-primary">View Details</a>
                    <button class="btn btn-secondary" onclick="shareLightboxItem('${title}')">Share</button>
                </div>
            </div>
            <button class="lightbox-close">&times;</button>
            ${galleryItems.length > 1 ? `
                <button class="lightbox-nav lightbox-prev" onclick="navigateLightbox(-1)">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="lightbox-nav lightbox-next" onclick="navigateLightbox(1)">
                    <i class="fas fa-chevron-right"></i>
                </button>
            ` : ''}
        </div>
    `;
    
    // Close functionality
    const closeBtn = lightbox.querySelector('.lightbox-close');
    closeBtn.addEventListener('click', closeLightbox);
    
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
            closeLightbox();
        }
    });
    
    // Keyboard navigation
    const keyHandler = function(e) {
        switch(e.key) {
            case 'Escape':
                closeLightbox();
                break;
            case 'ArrowLeft':
                navigateLightbox(-1);
                break;
            case 'ArrowRight':
                navigateLightbox(1);
                break;
        }
    };
    document.addEventListener('keydown', keyHandler);
    
    function closeLightbox() {
        lightbox.classList.remove('active');
        document.body.style.overflow = '';
        document.removeEventListener('keydown', keyHandler);
        
        setTimeout(() => {
            if (document.body.contains(lightbox)) {
                document.body.removeChild(lightbox);
            }
        }, 300);
    }
    
    return lightbox;
}

// Navigate lightbox
function navigateLightbox(direction) {
    if (galleryItems.length <= 1) return;
    
    currentLightboxIndex += direction;
    
    if (currentLightboxIndex < 0) {
        currentLightboxIndex = galleryItems.length - 1;
    } else if (currentLightboxIndex >= galleryItems.length) {
        currentLightboxIndex = 0;
    }
    
    const currentItem = galleryItems[currentLightboxIndex];
    const img = currentItem.querySelector('img');
    const category = currentItem.querySelector('.gallery-category').textContent;
    const title = currentItem.querySelector('h3').textContent;
    
    // Update lightbox content
    const lightbox = document.querySelector('.lightbox');
    const lightboxImage = lightbox.querySelector('.lightbox-image');
    const lightboxCategory = lightbox.querySelector('.lightbox-category');
    const lightboxTitle = lightbox.querySelector('.lightbox-info h3');
    const detailLink = lightbox.querySelector('.lightbox-actions a');
    
    lightboxImage.src = img.src;
    lightboxImage.alt = img.alt;
    lightboxCategory.textContent = category;
    lightboxTitle.textContent = title;
    detailLink.href = `item-detail.html?id=${generateItemId(category, title)}`;
}

// Generate item ID for detail page
function generateItemId(category, title) {
    const categoryMap = {
        'Divinity': 'div',
        'Artifacts': 'art',
        'Statues': 'stat'
    };
    
    const prefix = categoryMap[category] || 'item';
    const number = Math.floor(Math.random() * 999) + 1;
    return `${prefix}${number.toString().padStart(3, '0')}`;
}

// Share lightbox item
function shareLightboxItem(title) {
    if (navigator.share) {
        navigator.share({
            title: `${title} - Natha Moorti Museum`,
            text: `Check out this beautiful piece from Natha Moorti Museum`,
            url: window.location.href
        }).catch(console.error);
    } else {
        // Fallback: copy URL to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            showNotification('Link copied to clipboard!', 'success');
        }).catch(() => {
            showNotification('Unable to copy link', 'error');
        });
    }
}

// Show notification
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <div class="notification-icon">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            </div>
            <div class="notification-message">${message}</div>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 0;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 400px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        overflow: hidden;
    `;
    
    // Style notification content
    const content = notification.querySelector('.notification-content');
    content.style.cssText = `
        display: flex;
        align-items: center;
        padding: 15px 20px;
        gap: 12px;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Close functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', function() {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    });
    
    // Auto close after 3 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 3000);
}

// Search functionality (if search is added later)
function initGallerySearch() {
    const searchInput = document.querySelector('.gallery-search input');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const galleryItems = document.querySelectorAll('.gallery-item');
            
            galleryItems.forEach(item => {
                const title = item.querySelector('h3').textContent.toLowerCase();
                const category = item.querySelector('.gallery-category').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || category.includes(searchTerm)) {
                    item.style.display = 'block';
                    item.classList.remove('hidden');
                    item.classList.add('visible');
                } else {
                    item.style.display = 'none';
                    item.classList.add('hidden');
                    item.classList.remove('visible');
                }
            });
            
            updateGalleryItems();
        });
    }
}

// Infinite scroll (alternative to load more button)
function initInfiniteScroll() {
    let loading = false;
    
    window.addEventListener('scroll', function() {
        if (loading) return;
        
        const scrollTop = window.pageYOffset;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;
        
        if (scrollTop + windowHeight >= documentHeight - 1000) {
            loading = true;
            
            // Simulate loading more items
            setTimeout(() => {
                // In a real implementation, this would load more items
                loading = false;
            }, 1000);
        }
    });
}

// Initialize masonry layout properly
function initMasonry() {
    const galleryGrid = document.getElementById('galleryGrid');
    
    function updateMasonry() {
        if (galleryGrid.classList.contains('masonry')) {
            // Recalculate masonry layout
            const items = galleryGrid.querySelectorAll('.gallery-item');
            items.forEach(item => {
                item.style.breakInside = 'avoid';
            });
        }
    }
    
    // Update masonry on window resize
    window.addEventListener('resize', updateMasonry);
    
    // Update masonry when images load
    const images = galleryGrid.querySelectorAll('img');
    images.forEach(img => {
        img.addEventListener('load', updateMasonry);
    });
}
