/* Gallery Page Styles */

/* Page Header */
.page-header {
    padding: var(--spacing-xl) 0 var(--spacing-lg);
    margin-top: 120px;
    background-color: var(--gray-light);
    text-align: center;
}

.page-header-content {
    max-width: 600px;
    margin: 0 auto;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    color: var(--secondary-color);
    font-family: var(--font-accent);
    margin-bottom: var(--spacing-md);
}

.breadcrumb a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--primary-color);
}

.breadcrumb span {
    color: var(--gray-medium);
}

.page-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 300;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.page-description {
    font-size: 1.125rem;
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Gallery Filters */
.gallery-filters {
    padding: var(--spacing-lg) 0;
    background-color: var(--white);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 120px;
    z-index: 100;
}

.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.filter-tabs {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--secondary-color);
    font-family: var(--font-accent);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.view-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.view-btn {
    width: 40px;
    height: 40px;
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--secondary-color);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
}

.view-btn:hover,
.view-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

/* Gallery Section */
.gallery-section {
    padding: var(--spacing-xl) 0;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.gallery-grid.masonry {
    column-count: 4;
    column-gap: var(--spacing-md);
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    transition: var(--transition);
    break-inside: avoid;
    margin-bottom: var(--spacing-md);
}

.gallery-grid.masonry .gallery-item {
    display: inline-block;
    width: 100%;
    margin-bottom: var(--spacing-md);
}

.gallery-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
}

.gallery-image {
    position: relative;
    overflow: hidden;
    background-color: var(--gray-light);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    min-height: 300px;
}

.gallery-grid.masonry .gallery-image {
    min-height: auto;
    padding: var(--spacing-sm);
}

.gallery-image img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    object-position: center;
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.gallery-item:hover .gallery-image img {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    display: flex;
    align-items: flex-end;
    padding: var(--spacing-md);
    opacity: 0;
    transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-info {
    color: var(--white);
    width: 100%;
}

.gallery-category {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--accent-color);
    font-weight: 600;
    font-family: var(--font-accent);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.gallery-info h3 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-sm);
    color: var(--white);
    font-weight: 400;
}

.gallery-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.gallery-btn {
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--white);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    backdrop-filter: blur(10px);
}

.gallery-btn:hover {
    background-color: var(--white);
    border-color: var(--white);
    color: var(--primary-color);
    transform: scale(1.1);
}

/* Load More */
.load-more-container {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* Lightbox */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.lightbox-image {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: var(--border-radius);
}

.lightbox-info {
    background-color: var(--white);
    padding: var(--spacing-md);
    margin-top: var(--spacing-sm);
    text-align: center;
    border-radius: var(--border-radius);
    min-width: 300px;
}

.lightbox-category {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--accent-color);
    font-weight: 600;
    font-family: var(--font-accent);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.lightbox-info h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.lightbox-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    margin-top: var(--spacing-sm);
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.lightbox-close:hover {
    color: var(--accent-color);
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--white);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    backdrop-filter: blur(10px);
}

.lightbox-nav:hover {
    background-color: var(--white);
    color: var(--primary-color);
}

.lightbox-prev {
    left: 20px;
}

.lightbox-next {
    right: 20px;
}

/* Filter Animation */
.gallery-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.gallery-item.visible {
    opacity: 1;
    transform: scale(1);
    transition: all 0.3s ease;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .gallery-grid.masonry {
        column-count: 3;
    }
}

@media (max-width: 768px) {
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-tabs {
        justify-content: center;
    }
    
    .view-controls {
        justify-content: center;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--spacing-sm);
    }
    
    .gallery-grid.masonry {
        column-count: 2;
        column-gap: var(--spacing-sm);
    }
    
    .gallery-overlay {
        padding: var(--spacing-sm);
    }
    
    .lightbox-content {
        max-width: 95%;
        max-height: 95%;
    }
    
    .lightbox-info {
        min-width: auto;
        width: 100%;
    }
    
    .lightbox-nav {
        width: 40px;
        height: 40px;
    }
    
    .lightbox-prev {
        left: 10px;
    }
    
    .lightbox-next {
        right: 10px;
    }
}

@media (max-width: 480px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .gallery-grid.masonry {
        column-count: 1;
    }
    
    .filter-tabs {
        flex-direction: column;
        width: 100%;
    }
    
    .filter-btn {
        text-align: center;
    }
    
    .lightbox-close {
        top: -40px;
        right: -10px;
    }
}
