// Visit Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initVisitPage();
});

function initVisitPage() {
    initVisitAnimations();
    initContactMethods();
}

// Initialize animations for visit page elements
function initVisitAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                
                // Stagger animation for child elements
                const children = entry.target.querySelectorAll('.visit-card, .guideline-item, .facility-item');
                children.forEach((child, index) => {
                    setTimeout(() => {
                        child.classList.add('animate-in');
                    }, index * 100);
                });
            }
        });
    }, observerOptions);
    
    // Observe sections for animation
    const animateElements = document.querySelectorAll('.visit-info, .visitor-guidelines, .facilities');
    animateElements.forEach(el => {
        el.classList.add('animate-element');
        observer.observe(el);
    });
    
    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
        .animate-element {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
        }
        
        .animate-element.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        .visit-card,
        .guideline-item,
        .facility-item {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
        }
        
        .visit-card.animate-in,
        .guideline-item.animate-in,
        .facility-item.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        @media (prefers-reduced-motion: reduce) {
            .animate-element,
            .visit-card,
            .guideline-item,
            .facility-item {
                animation: none !important;
                transition: none !important;
                opacity: 1 !important;
                transform: none !important;
            }
        }
    `;
    document.head.appendChild(style);
}

// Initialize contact methods with click functionality
function initContactMethods() {
    const contactMethods = document.querySelectorAll('.contact-method');
    
    contactMethods.forEach(method => {
        const icon = method.querySelector('i');
        const text = method.querySelector('p').textContent;
        
        method.style.cursor = 'pointer';
        method.addEventListener('click', function() {
            if (icon.classList.contains('fa-phone')) {
                window.location.href = `tel:${text.replace(/\s/g, '')}`;
            } else if (icon.classList.contains('fa-envelope')) {
                window.location.href = `mailto:${text}`;
            }
        });
        
        method.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(10px)';
        });
        
        method.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
}

// Add smooth scrolling for internal links
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 120;
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Initialize smooth scrolling
document.addEventListener('DOMContentLoaded', initSmoothScrolling);
