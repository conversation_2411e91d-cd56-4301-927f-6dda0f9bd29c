/* Contact Page Specific Styles */

/* <PERSON> Header */
.page-header {
    position: relative;
    height: 60vh;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 120px;
    overflow: hidden;
}

.page-header-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.page-header-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(44, 24, 16, 0.8), rgba(139, 69, 19, 0.4));
}

.page-header-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white);
}

.breadcrumb {
    margin-bottom: 20px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.breadcrumb a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--white);
}

.breadcrumb span {
    margin: 0 10px;
    color: rgba(255, 255, 255, 0.6);
}

.page-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--white);
}

.page-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
}

/* Contact Information Section */
.contact-info-section {
    padding: 100px 0;
    background-color: var(--gray-light);
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.contact-card {
    background-color: var(--white);
    padding: 40px 30px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border-radius: 0;
}

.contact-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 30px;
    transition: var(--transition);
}

.contact-card:hover .contact-icon {
    background-color: var(--secondary-color);
    transform: scale(1.1);
}

.contact-card h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.contact-card p {
    color: var(--gray-dark);
    line-height: 1.6;
    margin-bottom: 20px;
}

.contact-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 1px;
    transition: var(--transition);
}

.contact-link:hover {
    color: var(--secondary-color);
}

/* Contact Form Section */
.contact-form-section {
    padding: 100px 0;
}

.contact-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-form {
    background-color: var(--gray-light);
    padding: 40px;
    border-radius: 0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid var(--gray-medium);
    background-color: var(--white);
    font-family: var(--font-secondary);
    font-size: 14px;
    transition: var(--transition);
    border-radius: 0;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
    color: var(--gray-dark);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-medium);
    background-color: var(--white);
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
    position: relative;
    transition: var(--transition);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid var(--white);
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Map Container */
.map-container {
    position: relative;
    height: 500px;
    background-color: var(--gray-light);
    border-radius: 0;
    overflow: hidden;
}

.map-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
}

.map-content {
    text-align: center;
    padding: 40px;
}

.map-content i {
    font-size: 60px;
    margin-bottom: 20px;
    opacity: 0.8;
}

.map-content h3 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: var(--white);
}

.map-content p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
}

.map-features {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.map-feature {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

.map-feature i {
    font-size: 16px;
    width: 20px;
}

/* FAQ Section */
.faq-section {
    padding: 100px 0;
    background-color: var(--gray-light);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background-color: var(--white);
    margin-bottom: 20px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.faq-item:hover {
    box-shadow: var(--shadow-hover);
}

.faq-question {
    padding: 25px 30px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.faq-question:hover {
    background-color: var(--gray-light);
}

.faq-question h3 {
    font-size: 1.2rem;
    color: var(--dark-color);
    margin-bottom: 0;
}

.faq-question i {
    font-size: 18px;
    color: var(--primary-color);
    transition: var(--transition);
}

.faq-item.active .faq-question i {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 0 30px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 0 30px 25px;
    max-height: 200px;
}

.faq-answer p {
    color: var(--gray-dark);
    line-height: 1.7;
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .contact-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }
    
    .contact-form-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .contact-form {
        padding: 30px 20px;
    }
    
    .map-container {
        height: 300px;
    }
    
    .map-content {
        padding: 20px;
    }
    
    .map-content i {
        font-size: 40px;
    }
    
    .map-content h3 {
        font-size: 1.4rem;
    }
    
    .map-features {
        gap: 10px;
    }
    
    .faq-question {
        padding: 20px;
    }
    
    .faq-answer {
        padding: 0 20px;
    }
    
    .faq-item.active .faq-answer {
        padding: 0 20px 20px;
    }
}

@media (max-width: 480px) {
    .page-header {
        height: 50vh;
        min-height: 300px;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .contact-info-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-card {
        padding: 30px 20px;
    }
    
    .contact-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 12px;
    }
    
    .map-content {
        padding: 15px;
    }
    
    .map-features {
        flex-direction: column;
        align-items: center;
    }
}
